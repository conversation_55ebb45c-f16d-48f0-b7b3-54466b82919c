import torch 
from arguments import get_args

def judge_correct(pred, label):
    args = get_args()
    diff = torch.abs(pred - label)
    power_correct = torch.where(torch.abs(diff[:, 0] / label[:, 0]) < args.epsilon_power, 1, 0).sum().item()
    area_correct = torch.where(torch.abs(diff[:, 1] / label[:, 1]) < args.epsilon_area , 1, 0).sum().item()
    # print("power", torch.exp(pred[:, 0]), label[:, 0])
    # print("area", torch.exp(pred[:, 1]), label[:, 1])
    return power_correct, area_correct
    

def test(dataloader, model, loss_fn, device='cpu'):

    size = len(dataloader.dataset)
    num_batches = len(dataloader)
    model.eval()
    test_loss = 0
    power_correct, area_correct = 0, 0
    with torch.no_grad():
        for embedding, label in dataloader:
            embedding, label = embedding.to(device), label.to(device)
            pred = model(embedding)
            correct_num = judge_correct(pred, label)
            power_correct += correct_num[0]
            area_correct += correct_num[1]
            test_loss += loss_fn(pred, label).item()
    test_loss /= num_batches
    power_correct /= size
    area_correct /= size
    print(f"Test average loss: {test_loss:>8f}, power tolerable pred num: {power_correct:>2f}, area tolerable pred num: {area_correct:>4f},  \n",)

    return test_loss, power_correct, area_correct