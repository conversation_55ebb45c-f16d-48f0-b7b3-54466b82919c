from train import train
from evaluate import test
from data_processing import create_dataloaders
from arguments import parse_args
from model import *
from plotting import *
import torch.nn as nn
import torch
import pandas as pd

if __name__ == '__main__':
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    args = parse_args()
    train_dataloader, test_dataloader = create_dataloaders(args)
    if args.model_name == 'mlp':
        print("Using MLP model")
        model = MLP(embed_dim=args.embed_dim).to(device)
    elif args.model_name == 'unet':
        print("Using UNet model")
        model = UNet(embed_dim=args.embed_dim).to(device)
    elif args.model_name == 'transformer':
        print("Using Transformer model")
        model = TransformerModel(hidden_size=args.embed_dim, nhead=args.nheads, num_layers=args.nlayers).to(device)
    else:
        raise NotImplementedError 
    
    loss_fn = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    scheduler = None
    
    epochs = args.epochs

    train_losses = []
    test_losses  = []
    test_acc = [[], []]
    for epoch in range(epochs):
        print(f"Epoch {epoch+1}\n-------------------------------")
        train_loss = train(dataloader=train_dataloader, model=model, loss_fn=loss_fn, optimizer=optimizer, scheduler=scheduler, device=device)
        test_loss, power_correct, area_correct = test(test_dataloader, model, loss_fn, device=device)
        train_losses.append(train_loss)
        test_losses.append(test_loss)
        test_acc[0].append(power_correct)
        test_acc[1].append(area_correct)

    plot_losses(train_losses, test_losses, epochs, args)
    plot_test_acc(test_acc, epochs, args)

    # Save results
    res = []
    res.append(train_losses)
    res.append(test_losses)
    res.append(test_acc[0])
    res.append(test_acc[1])

    pd.DataFrame(res, index=['train losses', 'test losses', 'power acc', 'area acc']).to_csv(f'./res/{args.model_name}-{args.test_fold}.csv')

    

    